'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, BlogPost, BlogCategory, BlogTag } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import BlogPostList from './BlogPostList';
import BlogPostEditor from './BlogPostEditor';
import CategoryManager from './CategoryManager';
import TagManager from './TagManager';
import BlogAnalytics from './BlogAnalytics';

type TabType = 'posts' | 'editor' | 'categories' | 'tags' | 'analytics';

export default function BlogManagement() {
  const [activeTab, setActiveTab] = useState<TabType>('posts');
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [tags, setTags] = useState<BlogTag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
  const { isAdmin } = useAuth();

  useEffect(() => {
    if (isAdmin) {
      loadData();
    }
  }, [isAdmin]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [postsData, categoriesData, tagsData] = await Promise.all([
        db.getBlogPosts({ limit: 50 }),
        db.getCategories(),
        db.getTags()
      ]);

      setPosts(postsData);
      setCategories(categoriesData);
      setTags(tagsData);
    } catch (error) {
      setError('Failed to load blog data');
      console.error('Error loading blog data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePostSaved = (post: BlogPost) => {
    setPosts(prev => {
      const existingIndex = prev.findIndex(p => p.id === post.id);
      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = post;
        return updated;
      } else {
        return [post, ...prev];
      }
    });
    setActiveTab('posts');
    setSelectedPost(null);
  };

  const handlePostDeleted = (postId: string) => {
    setPosts(prev => prev.filter(p => p.id !== postId));
  };

  const handleEditPost = (post: BlogPost) => {
    setSelectedPost(post);
    setActiveTab('editor');
  };

  const handleNewPost = () => {
    setSelectedPost(null);
    setActiveTab('editor');
  };

  if (!isAdmin) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-600">You do not have permission to access this page.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading blog management...</span>
      </div>
    );
  }

  const tabs = [
    { id: 'posts', label: 'Posts', icon: '📝', count: posts.length },
    { id: 'editor', label: 'Editor', icon: '✏️' },
    { id: 'categories', label: 'Categories', icon: '📁', count: categories.length },
    { id: 'tags', label: 'Tags', icon: '🏷️', count: tags.length },
    { id: 'analytics', label: 'Analytics', icon: '📊' },
  ] as const;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Blog Management
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Create, edit, and manage your blog content
          </p>
        </div>
        <button
          onClick={handleNewPost}
          className="mt-4 sm:mt-0 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <span className="mr-2">➕</span>
          New Post
        </button>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
              {tab.count !== undefined && (
                <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'posts' && (
          <BlogPostList
            posts={posts}
            onEdit={handleEditPost}
            onDelete={handlePostDeleted}
            onRefresh={loadData}
          />
        )}
        {activeTab === 'editor' && (
          <BlogPostEditor
            post={selectedPost}
            categories={categories}
            tags={tags}
            onSave={handlePostSaved}
            onCancel={() => {
              setActiveTab('posts');
              setSelectedPost(null);
            }}
          />
        )}
        {activeTab === 'categories' && (
          <CategoryManager
            categories={categories}
            onUpdate={setCategories}
          />
        )}
        {activeTab === 'tags' && (
          <TagManager
            tags={tags}
            onUpdate={setTags}
          />
        )}
        {activeTab === 'analytics' && (
          <BlogAnalytics posts={posts} />
        )}
      </motion.div>
    </div>
  );
}
