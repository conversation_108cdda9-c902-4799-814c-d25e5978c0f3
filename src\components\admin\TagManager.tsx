'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { BlogTag, BlogTagFormData, db } from '@/lib/supabase';

interface TagManagerProps {
  tags: BlogTag[];
  onUpdate: (tags: BlogTag[]) => void;
}

export default function TagManager({ tags, onUpdate }: TagManagerProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [formData, setFormData] = useState<BlogTagFormData>({
    name: '',
    slug: '',
    description: '',
    color: '#6B7280',
    is_active: true
  });
  const [loading, setLoading] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'name' | 'usage_count' | 'created_at'>('usage_count');

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#6B7280',
      is_active: true
    });
    setIsCreating(false);
    setEditingId(null);
    setError('');
  };

  const handleEdit = (tag: BlogTag) => {
    setFormData({
      name: tag.name,
      slug: tag.slug,
      description: tag.description || '',
      color: tag.color,
      is_active: tag.is_active
    });
    setEditingId(tag.id);
    setIsCreating(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.name.trim()) {
      setError('Tag name is required');
      return;
    }

    // Auto-generate slug if not provided
    if (!formData.slug) {
      formData.slug = formData.name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }

    const loadingId = editingId || 'new';
    setLoading(loadingId);

    try {
      let result: BlogTag | null;

      if (editingId) {
        result = await db.updateTag(editingId, formData);
      } else {
        result = await db.createTag(formData);
      }

      if (result) {
        // Refresh tags list
        const updatedTags = await db.getTags();
        onUpdate(updatedTags);
        resetForm();
      } else {
        setError('Failed to save tag');
      }
    } catch (error) {
      console.error('Error saving tag:', error);
      setError('Failed to save tag');
    } finally {
      setLoading(null);
    }
  };

  const handleDelete = async (tag: BlogTag) => {
    if (!confirm(`Are you sure you want to delete "${tag.name}"?`)) {
      return;
    }

    setLoading(tag.id);
    try {
      const success = await db.deleteTag(tag.id);
      if (success) {
        const updatedTags = await db.getTags();
        onUpdate(updatedTags);
      } else {
        setError('Failed to delete tag');
      }
    } catch (error) {
      console.error('Error deleting tag:', error);
      setError('Failed to delete tag');
    } finally {
      setLoading(null);
    }
  };

  const handleInputChange = (field: keyof BlogTagFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleBulkDelete = async () => {
    if (selectedTags.length === 0) return;

    if (!confirm(`Are you sure you want to delete ${selectedTags.length} tags?`)) {
      return;
    }

    setLoading('bulk');
    try {
      const deletePromises = selectedTags.map(id => db.deleteTag(id));
      await Promise.all(deletePromises);

      const updatedTags = await db.getTags();
      onUpdate(updatedTags);
      setSelectedTags([]);
    } catch (error) {
      console.error('Error bulk deleting tags:', error);
      setError('Failed to delete tags');
    } finally {
      setLoading(null);
    }
  };

  const handleSelectAll = () => {
    if (selectedTags.length === tags.length) {
      setSelectedTags([]);
    } else {
      setSelectedTags(tags.map(t => t.id));
    }
  };

  const sortedTags = [...tags].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'usage_count':
        return b.usage_count - a.usage_count;
      case 'created_at':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      default:
        return 0;
    }
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Tags ({tags.length})
          </h3>
          {selectedTags.length > 0 && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {selectedTags.length} selected
            </p>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Sort Options */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="usage_count">Usage Count</option>
            <option value="name">Name</option>
            <option value="created_at">Created Date</option>
          </select>

          {/* Bulk Actions */}
          {selectedTags.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={loading === 'bulk'}
              className="px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
            >
              {loading === 'bulk' ? 'Deleting...' : `Delete (${selectedTags.length})`}
            </button>
          )}

          <button
            onClick={() => setIsCreating(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Add Tag
          </button>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {/* Create/Edit Form */}
      {(isCreating || editingId) && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {editingId ? 'Edit Tag' : 'Create New Tag'}
          </h4>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Slug
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Auto-generated from name"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Brief description of the tag..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Color
                </label>
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => handleInputChange('color', e.target.value)}
                  className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"
                />
              </div>
              
              <div className="flex items-end">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Active</span>
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading === (editingId || 'new')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {loading === (editingId || 'new') ? 'Saving...' : (editingId ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {/* Select All */}
      {tags.length > 0 && (
        <div className="flex items-center mb-4">
          <input
            type="checkbox"
            checked={selectedTags.length === tags.length}
            onChange={handleSelectAll}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
          />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Select All ({tags.length})
          </span>
        </div>
      )}

      {/* Tags List */}
      <div className="flex flex-wrap gap-3">
        {sortedTags.map((tag) => (
          <motion.div
            key={tag.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="group relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 hover:shadow-md transition-all"
          >
            <div className="flex items-center space-x-2 mb-2">
              <input
                type="checkbox"
                checked={selectedTags.includes(tag.id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedTags(prev => [...prev, tag.id]);
                  } else {
                    setSelectedTags(prev => prev.filter(id => id !== tag.id));
                  }
                }}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: tag.color }}
              />
              <span className="font-medium text-gray-900 dark:text-white">
                {tag.name}
              </span>
              <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                {tag.usage_count}
              </span>
            </div>
            
            {tag.description && (
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                {tag.description}
              </p>
            )}
            
            <div className="flex items-center justify-between">
              <span className={`text-xs px-2 py-1 rounded-full ${
                tag.is_active 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}>
                {tag.is_active ? 'Active' : 'Inactive'}
              </span>
              
              <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                <button
                  onClick={() => handleEdit(tag)}
                  disabled={loading === tag.id}
                  className="p-1 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded text-xs"
                >
                  ✏️
                </button>
                <button
                  onClick={() => handleDelete(tag)}
                  disabled={loading === tag.id}
                  className="p-1 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded text-xs"
                >
                  {loading === tag.id ? '⏳' : '🗑️'}
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {tags.length === 0 && (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          No tags created yet. Create your first tag to get started!
        </div>
      )}
    </div>
  );
}
