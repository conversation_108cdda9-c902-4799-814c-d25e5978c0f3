'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BlogMedia, db, supabase } from '@/lib/supabase';
import Image from 'next/image';

interface MediaManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (media: BlogMedia) => void;
  selectedMediaIds?: string[];
}

export default function MediaManager({ isOpen, onClose, onSelect, selectedMediaIds = [] }: MediaManagerProps) {
  const [media, setMedia] = useState<BlogMedia[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'images' | 'videos' | 'documents'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    loadMedia();
  }, []);

  const loadMedia = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('blog_media')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setMedia(data || []);
    } catch (error) {
      console.error('Error loading media:', error);
      setError('Failed to load media');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    setUploading(true);
    setError('');

    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Generate unique filename
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `blog-media/${fileName}`;

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('blog-media')
          .upload(filePath, file);

        if (uploadError) throw uploadError;

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('blog-media')
          .getPublicUrl(filePath);

        // Save media record to database
        const mediaData = {
          filename: fileName,
          original_name: file.name,
          file_path: publicUrl,
          file_size: file.size,
          mime_type: file.type,
          width: null as number | null,
          height: null as number | null,
          alt_text: '',
          caption: '',
          uploaded_by: null, // Will be set by trigger if user is authenticated
          is_active: true
        };

        // If it's an image, get dimensions
        if (file.type.startsWith('image/')) {
          const dimensions = await getImageDimensions(file);
          mediaData.width = dimensions.width;
          mediaData.height = dimensions.height;
        }

        const { data: mediaRecord, error: dbError } = await supabase
          .from('blog_media')
          .insert([mediaData])
          .select()
          .single();

        if (dbError) throw dbError;
        return mediaRecord;
      });

      const uploadedMedia = await Promise.all(uploadPromises);
      setMedia(prev => [...uploadedMedia, ...prev]);
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      setError('Failed to upload files');
    } finally {
      setUploading(false);
    }
  };

  const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new globalThis.Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.src = URL.createObjectURL(file);
    });
  };

  const handleDelete = async (mediaItem: BlogMedia) => {
    if (!confirm('Are you sure you want to delete this media file?')) return;

    try {
      // Delete from storage
      const filePath = mediaItem.file_path.split('/').pop();
      if (filePath) {
        await supabase.storage
          .from('blog-media')
          .remove([`blog-media/${mediaItem.filename}`]);
      }

      // Delete from database
      const { error } = await supabase
        .from('blog_media')
        .delete()
        .eq('id', mediaItem.id);

      if (error) throw error;

      setMedia(prev => prev.filter(m => m.id !== mediaItem.id));
    } catch (error) {
      console.error('Error deleting media:', error);
      setError('Failed to delete media');
    }
  };

  const updateMediaInfo = async (mediaId: string, updates: Partial<BlogMedia>) => {
    try {
      const { error } = await supabase
        .from('blog_media')
        .update(updates)
        .eq('id', mediaId);

      if (error) throw error;

      setMedia(prev => prev.map(m => 
        m.id === mediaId ? { ...m, ...updates } : m
      ));
    } catch (error) {
      console.error('Error updating media:', error);
      setError('Failed to update media');
    }
  };

  const filteredMedia = media.filter(item => {
    const matchesFilter = filter === 'all' || 
      (filter === 'images' && item.mime_type.startsWith('image/')) ||
      (filter === 'videos' && item.mime_type.startsWith('video/')) ||
      (filter === 'documents' && item.mime_type.startsWith('application/'));
    
    const matchesSearch = searchTerm === '' ||
      item.original_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.alt_text?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.caption?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('application/pdf')) return '📄';
    return '📁';
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilter(e.target.value as 'all' | 'images' | 'videos' | 'documents');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Media Library ({media.length})
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Upload and manage your blog media files
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*,.pdf"
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            className="hidden"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {uploading ? 'Uploading...' : 'Upload Files'}
          </button>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-400 rounded-lg">
          {error}
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex space-x-2">
          {['all', 'images', 'videos', 'documents'].map((filterType) => (
            <button
              key={filterType}
              onClick={() => setFilter(filterType as 'all' | 'images' | 'videos' | 'documents')}
              className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${
                filter === filterType
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              {filterType}
            </button>
          ))}
        </div>
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search media..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Media Grid */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading media...</span>
        </div>
      ) : filteredMedia.length === 0 ? (
        <div className="text-center py-12 text-gray-500 dark:text-gray-400">
          {searchTerm ? 'No media found matching your search.' : 'No media files uploaded yet.'}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredMedia.map((item) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`bg-white dark:bg-gray-800 rounded-lg border-2 transition-all cursor-pointer ${
                selectedMediaIds.includes(item.id)
                  ? 'border-blue-500 shadow-lg'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => onSelect(item)}
            >
              {/* Media Preview */}
              <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-t-lg overflow-hidden relative">
                {item.mime_type.startsWith('image/') ? (
                  <Image
                    src={item.file_path}
                    alt={item.alt_text || item.filename}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-4xl">
                    {getFileIcon(item.mime_type)}
                  </div>
                )}
                
                {/* Actions */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(item);
                    }}
                    className="p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                  >
                    🗑️
                  </button>
                </div>
              </div>

              {/* Media Info */}
              <div className="p-3">
                <p className="font-medium text-gray-900 dark:text-white text-sm truncate">
                  {item.original_name}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {formatFileSize(item.file_size)}
                  {item.width && item.height && (
                    <span> • {item.width}×{item.height}</span>
                  )}
                </p>
                
                {/* Quick Edit */}
                <div className="mt-2 space-y-1">
                  <input
                    type="text"
                    placeholder="Alt text..."
                    value={item.alt_text || ''}
                    onChange={(e) => updateMediaInfo(item.id, { alt_text: e.target.value })}
                    className="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
}
